// 厂务管理系统通用JavaScript功能

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeTooltips();
    initializeNavigation();
    initializeRealTimeUpdates();
    initializeNotifications();
});

// 初始化工具提示
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化导航功能
function initializeNavigation() {
    // 设置当前页面的导航状态
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// 初始化实时数据更新
function initializeRealTimeUpdates() {
    // 模拟实时数据更新
    setInterval(updateRealTimeData, 30000); // 每30秒更新一次
}

// 更新实时数据
function updateRealTimeData() {
    // 这里可以添加AJAX调用来获取最新数据
    console.log('更新实时数据...');
    
    // 模拟数据更新
    updateEnergyConsumption();
    updateEquipmentStatus();
    updateAlarmCount();
}

// 更新能耗数据
function updateEnergyConsumption() {
    const energyElement = document.querySelector('.card .h5');
    if (energyElement && energyElement.textContent.includes('kWh')) {
        const currentValue = parseFloat(energyElement.textContent.replace(/[^\d.]/g, ''));
        const newValue = currentValue + Math.random() * 10 - 5; // 随机变化
        energyElement.textContent = newValue.toFixed(0) + ' kWh';
    }
}

// 更新设备状态
function updateEquipmentStatus() {
    const statusElement = document.querySelectorAll('.card .h5')[1];
    if (statusElement && statusElement.textContent.includes('%')) {
        const currentValue = parseFloat(statusElement.textContent.replace('%', ''));
        const newValue = Math.max(90, Math.min(100, currentValue + Math.random() * 2 - 1));
        statusElement.textContent = newValue.toFixed(1) + '%';
    }
}

// 更新报警数量
function updateAlarmCount() {
    const alarmElement = document.querySelectorAll('.card .h5')[2];
    if (alarmElement && !alarmElement.textContent.includes('kWh') && !alarmElement.textContent.includes('%')) {
        const currentValue = parseInt(alarmElement.textContent);
        const newValue = Math.max(0, currentValue + Math.floor(Math.random() * 3) - 1);
        alarmElement.textContent = newValue.toString();
    }
}

// 初始化通知系统
function initializeNotifications() {
    // 检查浏览器是否支持通知
    if ('Notification' in window) {
        if (Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
}

// 显示通知
function showNotification(title, message, type = 'info') {
    // 浏览器通知
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: message,
            icon: '/assets/images/logo.png'
        });
    }
    
    // 页面内通知
    showToast(title, message, type);
}

// 显示Toast通知
function showToast(title, message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong><br>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // 自动移除Toast元素
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// 获取或创建Toast容器
function getOrCreateToastContainer() {
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
    }
    return container;
}

// 格式化数字
function formatNumber(num, decimals = 0) {
    return num.toLocaleString('zh-CN', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

// 格式化日期时间
function formatDateTime(date) {
    return new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).format(date);
}

// 获取状态标签HTML
function getStatusBadge(status) {
    const statusMap = {
        'online': { class: 'status-online', text: '在线' },
        'offline': { class: 'status-offline', text: '离线' },
        'warning': { class: 'status-warning', text: '警告' },
        'maintenance': { class: 'status-maintenance', text: '维护' }
    };
    
    const statusInfo = statusMap[status] || { class: 'status-offline', text: '未知' };
    return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

// AJAX请求封装
function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    const config = { ...defaultOptions, ...options };
    
    return fetch(url, config)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('API请求失败:', error);
            showNotification('错误', '数据请求失败，请稍后重试', 'danger');
            throw error;
        });
}

// 导出数据功能
function exportData(data, filename, type = 'csv') {
    let content, mimeType;
    
    if (type === 'csv') {
        content = convertToCSV(data);
        mimeType = 'text/csv;charset=utf-8;';
    } else if (type === 'json') {
        content = JSON.stringify(data, null, 2);
        mimeType = 'application/json;charset=utf-8;';
    }
    
    const blob = new Blob([content], { type: mimeType });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 转换为CSV格式
function convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    return csvContent;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('全局错误:', e.error);
    showNotification('系统错误', '发生了未预期的错误，请刷新页面重试', 'danger');
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('页面隐藏，暂停实时更新');
    } else {
        console.log('页面显示，恢复实时更新');
        updateRealTimeData();
    }
});
