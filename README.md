# 厂务管理系统 - 前端原型

## 项目概述

这是一个完整的厂务管理系统前端原型，采用现代化的Web设计规范，提供了完整的用户界面和交互体验。系统涵盖了工厂设施管理的各个方面，包括能源管理、设备监控、环境监测、报警管理等核心功能。

## 功能模块

### 1. 首页 (index.html)
- 统一服务入口
- 多系统数据集成看板
- 关键指标展示
- 实时数据图表

### 2. 能源管理系统 (energy-management.html)
- 电、水、气、蒸汽等能源的实时监控
- 分项计量与能耗对标
- 能耗趋势分析
- 节能策略推荐

### 3. 动力设备管理 (equipment-monitoring.html)
- 空压机、冷水机组、锅炉、水泵等设备监控
- 运行状态实时显示
- 设备控制操作
- 故障报警管理

### 4. 环境监测系统 (environment-monitoring.html)
- 温湿度、洁净度、照度等环境参数监测
- 有害气体监测
- 环境报警管理
- 合规性分析

### 5. 设备台账与生命周期管理 (equipment-lifecycle.html)
- 完整的设备台账管理
- 设备生命周期跟踪
- 维护计划管理
- 供应商信息管理

### 6. 报警与事件管理 (alarm-management.html)
- 多类型报警管理
- 报警分级分类
- 事件处理流程
- 闭环追踪

### 7. 可视化调度大屏 (dashboard.html)
- 全屏大屏展示
- 实时数据可视化
- 关键指标监控
- 设备状态总览

### 8. 运维工单管理 (work-order.html)
- 工单全流程管理
- 任务分配与跟踪
- 维修记录管理
- KPI统计分析

## 技术特性

### 前端技术栈
- **HTML5** - 语义化标记
- **CSS3** - 现代化样式设计
- **Bootstrap 5** - 响应式框架
- **JavaScript ES6+** - 交互逻辑
- **Chart.js** - 数据可视化
- **FontAwesome** - 图标库

### 设计特点
- **响应式设计** - 支持多种设备尺寸
- **现代化UI** - 采用卡片式布局和渐变效果
- **统一导航** - 顶部导航栏和侧边栏导航
- **实时数据** - 模拟实时数据更新
- **交互友好** - 丰富的交互效果和动画

### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 项目结构

```
厂务管理系统/
├── index.html                    # 主入口页面
├── energy-management.html        # 能源管理
├── equipment-monitoring.html     # 设备监控
├── environment-monitoring.html   # 环境监测
├── equipment-lifecycle.html      # 设备台账
├── alarm-management.html         # 报警管理
├── dashboard.html                # 可视化大屏
├── work-order.html               # 运维工单
├── assets/                       # 静态资源
│   ├── css/
│   │   ├── common.css            # 通用样式
│   │   └── dashboard.css         # 大屏专用样式
│   └── js/
│       ├── common.js             # 通用功能
│       ├── charts.js             # 图表配置
│       ├── energy-charts.js      # 能源图表
│       ├── environment-charts.js # 环境图表
│       ├── dashboard.js          # 大屏功能
│       └── dashboard-charts.js   # 大屏图表
└── README.md                     # 项目说明
```

## 快速开始

### 1. 环境要求
- 现代化Web浏览器
- 本地Web服务器（推荐）

### 2. 运行方式

#### 方式一：直接打开
直接用浏览器打开 `index.html` 文件即可访问系统。

#### 方式二：本地服务器（推荐）
```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 然后访问 http://localhost:8000
```

### 3. 功能演示
1. 打开 `index.html` 查看系统首页
2. 通过导航栏访问各个功能模块
3. 点击 `dashboard.html` 体验全屏大屏效果
4. 各页面支持实时数据模拟更新

## 核心功能说明

### 数据可视化
- 使用Chart.js实现多种图表类型
- 支持实时数据更新
- 响应式图表设计
- 深色主题适配

### 用户交互
- 统一的导航体验
- 模态框表单操作
- 表格排序和筛选
- 按钮状态反馈

### 响应式设计
- 移动端友好
- 平板适配
- 大屏优化
- 弹性布局

## 自定义配置

### 修改主题色彩
在 `assets/css/common.css` 中修改CSS变量：
```css
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    /* 其他颜色变量 */
}
```

### 添加新页面
1. 复制现有页面作为模板
2. 修改页面内容和导航链接
3. 更新侧边栏导航菜单
4. 添加对应的JavaScript功能

### 集成后端API
在 `assets/js/common.js` 中的 `apiRequest` 函数基础上：
1. 修改API端点URL
2. 添加认证头信息
3. 处理实际数据格式
4. 实现错误处理逻辑

## 部署说明

### 静态部署
本项目为纯前端项目，可直接部署到任何静态文件服务器：
- Nginx
- Apache
- GitHub Pages
- Netlify
- Vercel

### 生产环境优化
1. 压缩CSS和JavaScript文件
2. 优化图片资源
3. 启用Gzip压缩
4. 配置CDN加速
5. 添加缓存策略

## 开发指南

### 代码规范
- 使用语义化HTML标签
- CSS采用BEM命名规范
- JavaScript使用ES6+语法
- 注释清晰完整

### 扩展建议
1. 添加用户认证系统
2. 集成WebSocket实现真实时数据
3. 添加数据导出功能
4. 实现离线缓存
5. 添加国际化支持

## 许可证

本项目仅供学习和演示使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件咨询

---

**注意**: 本项目为前端原型演示，所有数据均为模拟数据，实际使用时需要集成真实的后端API服务。
