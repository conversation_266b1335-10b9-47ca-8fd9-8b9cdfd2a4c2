// 可视化大屏图表配置

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboardCharts();
});

// 初始化大屏图表
function initializeDashboardCharts() {
    initializeRealTimeEnergyChart();
    initializeEquipmentStatusChart();
}

// 初始化实时能耗图表
function initializeRealTimeEnergyChart() {
    const ctx = document.getElementById('realTimeEnergyChart');
    if (!ctx) return;

    // 生成初始数据
    const labels = [];
    const data = [];
    const now = new Date();
    
    for (let i = 19; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60000); // 每分钟一个数据点
        labels.push(time.toLocaleTimeString());
        data.push(Math.floor(Math.random() * 200) + 100);
    }

    window.realTimeEnergyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '实时功率 (kW)',
                data: data,
                borderColor: '#ffd700',
                backgroundColor: 'rgba(255, 215, 0, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#ffd700',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 0,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#ffd700',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return `功率: ${context.parsed.y} kW`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        display: true,
                        color: 'rgba(255, 255, 255, 0.1)',
                        lineWidth: 1
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 11
                        },
                        maxTicksLimit: 8
                    }
                },
                y: {
                    display: true,
                    grid: {
                        display: true,
                        color: 'rgba(255, 255, 255, 0.1)',
                        lineWidth: 1
                    },
                    ticks: {
                        color: 'rgba(255, 255, 255, 0.7)',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value + ' kW';
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 0 // 禁用动画以提高实时性能
            },
            elements: {
                line: {
                    borderJoinStyle: 'round'
                }
            }
        }
    });
}

// 初始化设备状态饼图
function initializeEquipmentStatusChart() {
    const ctx = document.getElementById('equipmentStatusChart');
    if (!ctx) return;

    window.equipmentStatusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['正常运行', '维护中', '故障', '离线'],
            datasets: [{
                data: [42, 4, 1, 1],
                backgroundColor: [
                    '#00ff88',
                    '#ffd700',
                    '#ff6b6b',
                    '#6c757d'
                ],
                borderColor: [
                    '#00ff88',
                    '#ffd700',
                    '#ff6b6b',
                    '#6c757d'
                ],
                borderWidth: 2,
                hoverOffset: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12
                        },
                        color: 'rgba(255, 255, 255, 0.8)'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#ffffff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value}台 (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '65%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1500,
                easing: 'easeInOutQuart'
            }
        }
    });

    // 在中心显示总数
    const centerTextPlugin = {
        id: 'centerText',
        beforeDraw: function(chart) {
            const ctx = chart.ctx;
            const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
            const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2;
            
            const total = chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
            
            ctx.save();
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#ffffff';
            ctx.fillText(total, centerX, centerY - 10);
            
            ctx.font = '14px Arial';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.fillText('设备总数', centerX, centerY + 15);
            ctx.restore();
        }
    };
    
    Chart.register(centerTextPlugin);
}

// 创建温度仪表盘
function createTemperatureGauge(canvasId, value, max = 50) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    return new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [value, max - value],
                backgroundColor: [
                    value > 30 ? '#ff6b6b' : value > 25 ? '#ffd700' : '#00ff88',
                    'rgba(255, 255, 255, 0.1)'
                ],
                borderWidth: 0,
                cutout: '80%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            },
            rotation: -90,
            circumference: 180
        },
        plugins: [{
            beforeDraw: function(chart) {
                const ctx = chart.ctx;
                const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
                const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2;

                ctx.save();
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = 'bold 20px Arial';
                ctx.fillStyle = '#ffffff';
                ctx.fillText(value + '°C', centerX, centerY - 5);
                
                ctx.font = '12px Arial';
                ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                ctx.fillText('温度', centerX, centerY + 15);
                ctx.restore();
            }
        }]
    });
}

// 更新图表主题（深色主题）
function applyDarkTheme(chart) {
    const options = chart.options;
    
    // 更新网格颜色
    if (options.scales) {
        Object.keys(options.scales).forEach(scaleKey => {
            const scale = options.scales[scaleKey];
            if (scale.grid) {
                scale.grid.color = 'rgba(255, 255, 255, 0.1)';
            }
            if (scale.ticks) {
                scale.ticks.color = 'rgba(255, 255, 255, 0.7)';
            }
        });
    }
    
    // 更新图例颜色
    if (options.plugins && options.plugins.legend && options.plugins.legend.labels) {
        options.plugins.legend.labels.color = 'rgba(255, 255, 255, 0.8)';
    }
    
    chart.update();
}

// 图表数据更新函数
function updateChartWithNewData(chart, newData) {
    if (!chart || !chart.data) return;
    
    const data = chart.data;
    const now = new Date();
    
    // 添加新数据点
    data.labels.push(now.toLocaleTimeString());
    data.datasets[0].data.push(newData);
    
    // 保持固定数量的数据点
    const maxDataPoints = 20;
    if (data.labels.length > maxDataPoints) {
        data.labels.shift();
        data.datasets[0].data.shift();
    }
    
    chart.update('none'); // 无动画更新以提高性能
}

// 图表性能优化
function optimizeChartPerformance(chart) {
    // 禁用动画
    chart.options.animation = false;
    chart.options.animations = false;
    chart.options.responsiveAnimationDuration = 0;
    
    // 减少重绘
    chart.options.elements = {
        ...chart.options.elements,
        point: {
            radius: 0,
            hoverRadius: 4
        }
    };
    
    chart.update();
}

// 应用大屏主题到所有图表
setTimeout(() => {
    if (window.realTimeEnergyChart) {
        applyDarkTheme(window.realTimeEnergyChart);
        optimizeChartPerformance(window.realTimeEnergyChart);
    }
    if (window.equipmentStatusChart) {
        applyDarkTheme(window.equipmentStatusChart);
    }
}, 100);
