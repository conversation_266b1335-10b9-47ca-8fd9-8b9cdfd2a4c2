<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可视化调度大屏 - 厂务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/common.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-fullscreen">
    <!-- 顶部状态栏 -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <h1 class="dashboard-title">
                        <i class="fas fa-industry me-2"></i>厂务管理系统 - 可视化大屏
                    </h1>
                </div>
                <div class="col-md-4 text-center">
                    <div class="current-time" id="currentTime"></div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="dashboard-controls">
                        <button class="btn btn-outline-light btn-sm me-2" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> 全屏
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="window.location.href='index.html'">
                            <i class="fas fa-times"></i> 退出
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
        <div class="container-fluid">
            <!-- 第一行：关键指标 -->
            <div class="row mb-3">
                <div class="col-xl-3 col-lg-6 mb-3">
                    <div class="dashboard-card energy-card">
                        <div class="card-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">总能耗</h3>
                            <div class="card-value">2,847 <span class="unit">kWh</span></div>
                            <div class="card-trend trend-down">
                                <i class="fas fa-arrow-down"></i> 5.2%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 mb-3">
                    <div class="dashboard-card equipment-card">
                        <div class="card-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">设备运行率</h3>
                            <div class="card-value">96.8 <span class="unit">%</span></div>
                            <div class="card-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 2.1%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 mb-3">
                    <div class="dashboard-card alarm-card">
                        <div class="card-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">活跃报警</h3>
                            <div class="card-value">3 <span class="unit">条</span></div>
                            <div class="card-trend trend-down">
                                <i class="fas fa-arrow-down"></i> 40%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6 mb-3">
                    <div class="dashboard-card environment-card">
                        <div class="card-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">环境指数</h3>
                            <div class="card-value">85 <span class="unit">分</span></div>
                            <div class="card-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 3.5%
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二行：图表区域 -->
            <div class="row mb-3">
                <div class="col-xl-8 col-lg-7 mb-3">
                    <div class="dashboard-chart-card">
                        <div class="chart-header">
                            <h4><i class="fas fa-chart-line me-2"></i>实时能耗监控</h4>
                            <div class="chart-controls">
                                <button class="btn btn-sm btn-outline-primary active">电力</button>
                                <button class="btn btn-sm btn-outline-primary">水资源</button>
                                <button class="btn btn-sm btn-outline-primary">天然气</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="realTimeEnergyChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-lg-5 mb-3">
                    <div class="dashboard-chart-card">
                        <div class="chart-header">
                            <h4><i class="fas fa-chart-pie me-2"></i>设备状态分布</h4>
                        </div>
                        <div class="chart-container">
                            <canvas id="equipmentStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三行：设备监控和报警 -->
            <div class="row">
                <div class="col-xl-6 col-lg-6 mb-3">
                    <div class="dashboard-list-card">
                        <div class="list-header">
                            <h4><i class="fas fa-cogs me-2"></i>关键设备状态</h4>
                        </div>
                        <div class="equipment-list">
                            <div class="equipment-item status-running">
                                <div class="equipment-info">
                                    <div class="equipment-name">空压机#1</div>
                                    <div class="equipment-location">动力车间A</div>
                                </div>
                                <div class="equipment-status">
                                    <div class="status-indicator"></div>
                                    <div class="status-text">运行中</div>
                                </div>
                                <div class="equipment-metrics">
                                    <div class="metric">
                                        <span class="metric-label">负荷</span>
                                        <span class="metric-value">75%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">温度</span>
                                        <span class="metric-value">45°C</span>
                                    </div>
                                </div>
                            </div>
                            <div class="equipment-item status-running">
                                <div class="equipment-info">
                                    <div class="equipment-name">冷水机组#1</div>
                                    <div class="equipment-location">制冷站</div>
                                </div>
                                <div class="equipment-status">
                                    <div class="status-indicator"></div>
                                    <div class="status-text">运行中</div>
                                </div>
                                <div class="equipment-metrics">
                                    <div class="metric">
                                        <span class="metric-label">负荷</span>
                                        <span class="metric-value">85%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">温度</span>
                                        <span class="metric-value">7°C</span>
                                    </div>
                                </div>
                            </div>
                            <div class="equipment-item status-maintenance">
                                <div class="equipment-info">
                                    <div class="equipment-name">蒸汽锅炉#1</div>
                                    <div class="equipment-location">锅炉房</div>
                                </div>
                                <div class="equipment-status">
                                    <div class="status-indicator"></div>
                                    <div class="status-text">维护中</div>
                                </div>
                                <div class="equipment-metrics">
                                    <div class="metric">
                                        <span class="metric-label">负荷</span>
                                        <span class="metric-value">0%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">温度</span>
                                        <span class="metric-value">25°C</span>
                                    </div>
                                </div>
                            </div>
                            <div class="equipment-item status-fault">
                                <div class="equipment-info">
                                    <div class="equipment-name">空压机#2</div>
                                    <div class="equipment-location">动力车间B</div>
                                </div>
                                <div class="equipment-status">
                                    <div class="status-indicator"></div>
                                    <div class="status-text">故障</div>
                                </div>
                                <div class="equipment-metrics">
                                    <div class="metric">
                                        <span class="metric-label">负荷</span>
                                        <span class="metric-value">0%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">温度</span>
                                        <span class="metric-value">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6 col-lg-6 mb-3">
                    <div class="dashboard-list-card">
                        <div class="list-header">
                            <h4><i class="fas fa-bell me-2"></i>实时报警信息</h4>
                        </div>
                        <div class="alarm-list">
                            <div class="alarm-item alarm-critical">
                                <div class="alarm-icon">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <div class="alarm-content">
                                    <div class="alarm-title">空压机#2 压力异常</div>
                                    <div class="alarm-detail">压力值: 12.5 bar (正常: 8-10 bar)</div>
                                    <div class="alarm-time">15分钟前</div>
                                </div>
                                <div class="alarm-status">
                                    <span class="status-badge critical">严重</span>
                                </div>
                            </div>
                            <div class="alarm-item alarm-warning">
                                <div class="alarm-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alarm-content">
                                    <div class="alarm-title">车间B 温度偏高</div>
                                    <div class="alarm-detail">当前温度: 28.5°C (正常: 20-25°C)</div>
                                    <div class="alarm-time">20分钟前</div>
                                </div>
                                <div class="alarm-status">
                                    <span class="status-badge warning">警告</span>
                                </div>
                            </div>
                            <div class="alarm-item alarm-info">
                                <div class="alarm-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="alarm-content">
                                    <div class="alarm-title">冷水机组#1 负荷较高</div>
                                    <div class="alarm-detail">当前负荷: 85% (建议: <80%)</div>
                                    <div class="alarm-time">25分钟前</div>
                                </div>
                                <div class="alarm-status">
                                    <span class="status-badge info">提示</span>
                                </div>
                            </div>
                            <div class="alarm-item alarm-warning">
                                <div class="alarm-icon">
                                    <i class="fas fa-wifi"></i>
                                </div>
                                <div class="alarm-content">
                                    <div class="alarm-title">数据网关通信异常</div>
                                    <div class="alarm-detail">网关 GW-003 连接超时</div>
                                    <div class="alarm-time">30分钟前</div>
                                </div>
                                <div class="alarm-status">
                                    <span class="status-badge warning">警告</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/common.js"></script>
    <script src="assets/js/dashboard-charts.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
