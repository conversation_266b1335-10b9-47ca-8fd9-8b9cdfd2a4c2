// 可视化大屏JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    startRealTimeUpdates();
    updateCurrentTime();
});

// 初始化大屏
function initializeDashboard() {
    console.log('初始化可视化大屏...');
    
    // 设置全屏模式
    if (window.location.search.includes('fullscreen=true')) {
        enterFullscreen();
    }
    
    // 初始化实时数据更新
    updateDashboardData();
}

// 更新当前时间
function updateCurrentTime() {
    const timeElement = document.getElementById('currentTime');
    if (!timeElement) return;
    
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            weekday: 'long'
        });
        timeElement.textContent = timeString;
    }
    
    updateTime();
    setInterval(updateTime, 1000);
}

// 开始实时数据更新
function startRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateDashboardData, 30000);
    
    // 每5秒更新一次图表数据
    setInterval(updateChartData, 5000);
}

// 更新大屏数据
function updateDashboardData() {
    updateEnergyData();
    updateEquipmentData();
    updateAlarmData();
    updateEnvironmentData();
}

// 更新能耗数据
function updateEnergyData() {
    const energyValue = document.querySelector('.energy-card .card-value');
    if (energyValue) {
        const currentValue = parseFloat(energyValue.textContent.replace(/[^\d.]/g, ''));
        const newValue = currentValue + (Math.random() * 20 - 10); // 随机变化
        energyValue.innerHTML = `${Math.max(0, newValue).toFixed(0)} <span class="unit">kWh</span>`;
    }
}

// 更新设备数据
function updateEquipmentData() {
    const equipmentValue = document.querySelector('.equipment-card .card-value');
    if (equipmentValue) {
        const currentValue = parseFloat(equipmentValue.textContent.replace('%', ''));
        const newValue = currentValue + (Math.random() * 2 - 1); // 小幅随机变化
        equipmentValue.innerHTML = `${Math.max(90, Math.min(100, newValue)).toFixed(1)} <span class="unit">%</span>`;
    }
}

// 更新报警数据
function updateAlarmData() {
    const alarmValue = document.querySelector('.alarm-card .card-value');
    if (alarmValue) {
        const currentValue = parseInt(alarmValue.textContent);
        const newValue = Math.max(0, currentValue + Math.floor(Math.random() * 3) - 1);
        alarmValue.innerHTML = `${newValue} <span class="unit">条</span>`;
    }
}

// 更新环境数据
function updateEnvironmentData() {
    const environmentValue = document.querySelector('.environment-card .card-value');
    if (environmentValue) {
        const currentValue = parseInt(environmentValue.textContent);
        const newValue = currentValue + Math.floor(Math.random() * 6) - 3; // 随机变化
        environmentValue.innerHTML = `${Math.max(70, Math.min(100, newValue))} <span class="unit">分</span>`;
    }
}

// 更新图表数据
function updateChartData() {
    // 更新实时能耗图表
    if (window.realTimeEnergyChart) {
        const now = new Date();
        const newValue = Math.floor(Math.random() * 200) + 100;
        
        const data = window.realTimeEnergyChart.data;
        data.labels.push(now.toLocaleTimeString());
        data.datasets[0].data.push(newValue);
        
        // 保持最近20个数据点
        if (data.labels.length > 20) {
            data.labels.shift();
            data.datasets[0].data.shift();
        }
        
        window.realTimeEnergyChart.update('none');
    }
}

// 全屏功能
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        enterFullscreen();
    } else {
        exitFullscreen();
    }
}

function enterFullscreen() {
    const element = document.documentElement;
    if (element.requestFullscreen) {
        element.requestFullscreen();
    } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
    } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
    }
}

function exitFullscreen() {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
    }
}

// 监听全屏状态变化
document.addEventListener('fullscreenchange', function() {
    const fullscreenBtn = document.querySelector('.dashboard-controls .btn');
    if (fullscreenBtn) {
        if (document.fullscreenElement) {
            fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i> 退出全屏';
        } else {
            fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i> 全屏';
        }
    }
});

// 设备状态动画
function animateEquipmentStatus() {
    const equipmentItems = document.querySelectorAll('.equipment-item');
    equipmentItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 100);
        }, index * 100);
    });
}

// 报警闪烁效果
function startAlarmBlinking() {
    const criticalAlarms = document.querySelectorAll('.alarm-critical');
    
    setInterval(() => {
        criticalAlarms.forEach(alarm => {
            alarm.style.animation = 'none';
            setTimeout(() => {
                alarm.style.animation = 'pulse 1s ease-in-out';
            }, 10);
        });
    }, 3000);
}

// 数据加载动画
function showLoadingAnimation(element) {
    const originalContent = element.innerHTML;
    element.innerHTML = '<div class="loading-spinner"></div>';
    
    setTimeout(() => {
        element.innerHTML = originalContent;
    }, 1000);
}

// 错误处理
function handleDashboardError(error) {
    console.error('大屏数据更新错误:', error);
    showNotification('数据更新失败', '请检查网络连接', 'danger');
}

// 性能监控
function monitorPerformance() {
    const startTime = performance.now();
    
    return function() {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        if (duration > 1000) {
            console.warn(`大屏更新耗时过长: ${duration.toFixed(2)}ms`);
        }
    };
}

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    switch(event.key) {
        case 'F11':
            event.preventDefault();
            toggleFullscreen();
            break;
        case 'Escape':
            if (document.fullscreenElement) {
                exitFullscreen();
            }
            break;
        case 'r':
        case 'R':
            if (event.ctrlKey) {
                event.preventDefault();
                updateDashboardData();
                showNotification('数据刷新', '已手动刷新数据', 'success');
            }
            break;
    }
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('大屏页面隐藏，暂停更新');
        // 可以在这里暂停一些不必要的更新
    } else {
        console.log('大屏页面显示，恢复更新');
        updateDashboardData();
    }
});

// 窗口大小变化处理
window.addEventListener('resize', function() {
    // 重新调整图表大小
    if (window.realTimeEnergyChart) {
        window.realTimeEnergyChart.resize();
    }
    if (window.equipmentStatusChart) {
        window.equipmentStatusChart.resize();
    }
});

// 初始化动画效果
setTimeout(() => {
    animateEquipmentStatus();
    startAlarmBlinking();
}, 1000);

// 导出全局函数
window.toggleFullscreen = toggleFullscreen;
window.updateDashboardData = updateDashboardData;
