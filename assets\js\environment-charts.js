// 环境监测页面图表配置

document.addEventListener('DOMContentLoaded', function() {
    initializeEnvironmentTrendChart();
});

// 初始化环境参数趋势图表
function initializeEnvironmentTrendChart() {
    const ctx = document.getElementById('environmentTrendChart');
    if (!ctx) return;

    // 生成24小时数据
    const labels = [];
    const temperatureData = [];
    const humidityData = [];
    const aqiData = [];
    
    for (let i = 0; i < 24; i++) {
        labels.push(i + ':00');
        temperatureData.push(Math.floor(Math.random() * 8) + 20); // 20-28°C
        humidityData.push(Math.floor(Math.random() * 20) + 50); // 50-70%
        aqiData.push(Math.floor(Math.random() * 30) + 70); // 70-100 AQI
    }

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '温度 (°C)',
                    data: temperatureData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y'
                },
                {
                    label: '湿度 (%)',
                    data: humidityData,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1'
                },
                {
                    label: '空气质量指数',
                    data: aqiData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y2'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间',
                        color: '#666',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '温度 (°C)',
                        color: '#dc3545',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    min: 15,
                    max: 35
                },
                y1: {
                    type: 'linear',
                    display: false,
                    position: 'right',
                    min: 40,
                    max: 80,
                    grid: {
                        drawOnChartArea: false,
                    },
                },
                y2: {
                    type: 'linear',
                    display: false,
                    position: 'right',
                    min: 60,
                    max: 120,
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}
