/* 厂务管理系统通用样式 */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 250px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    padding-top: 56px;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.15);
    border-right: 3px solid var(--primary-color);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* 主内容区域 */
.main-content {
    padding-top: 20px;
}

main {
    min-height: calc(100vh - 76px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 10px 10px 0 0 !important;
}

/* 边框颜色卡片 */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
}

/* 文本样式 */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 表格样式 */
.table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    font-weight: 600;
    color: #5a5c69;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fc;
}

/* 状态标签 */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-online {
    background-color: #d4edda;
    color: #155724;
}

.status-offline {
    background-color: #f8d7da;
    color: #721c24;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-maintenance {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    .main-content {
        padding-top: 10px;
    }
    
    main {
        padding: 0 15px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #333;
    color: white;
    border-radius: 4px;
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fc;
    border-radius: 10px 10px 0 0;
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #d1d3e2;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
