// 图表配置和初始化

// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

// 初始化所有图表
function initializeCharts() {
    initializeEnergyChart();
    initializeEquipmentChart();
}

// 初始化能耗趋势图表
function initializeEnergyChart() {
    const ctx = document.getElementById('energyChart');
    if (!ctx) return;

    // 生成模拟数据
    const labels = [];
    const data = [];
    const now = new Date();
    
    for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        labels.push(time.getHours() + ':00');
        data.push(Math.floor(Math.random() * 500) + 1500);
    }

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '能耗 (kWh)',
                data: data,
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return `能耗: ${context.parsed.y} kWh`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间',
                        color: '#666',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#666',
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '能耗 (kWh)',
                        color: '#666',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    },
                    ticks: {
                        color: '#666',
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return value + ' kWh';
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// 初始化设备状态饼图
function initializeEquipmentChart() {
    const ctx = document.getElementById('equipmentChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['正常运行', '维护中', '故障', '离线'],
            datasets: [{
                data: [85, 8, 3, 4],
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#dc3545',
                    '#6c757d'
                ],
                borderColor: [
                    '#28a745',
                    '#17a2b8',
                    '#dc3545',
                    '#6c757d'
                ],
                borderWidth: 2,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value}台 (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// 创建实时更新的线图
function createRealTimeChart(canvasId, label, color) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    const data = {
        labels: [],
        datasets: [{
            label: label,
            data: [],
            borderColor: color,
            backgroundColor: color + '20',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    };

    return new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'minute',
                        displayFormats: {
                            minute: 'HH:mm'
                        }
                    }
                },
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            },
            animation: {
                duration: 0
            }
        }
    });
}

// 更新实时图表数据
function updateRealTimeChart(chart, newValue) {
    if (!chart) return;

    const now = new Date();
    const data = chart.data;

    // 添加新数据点
    data.labels.push(now);
    data.datasets[0].data.push(newValue);

    // 保持最近50个数据点
    if (data.labels.length > 50) {
        data.labels.shift();
        data.datasets[0].data.shift();
    }

    chart.update('none');
}

// 创建仪表盘图表
function createGaugeChart(canvasId, value, max, label, color) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    return new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [value, max - value],
                backgroundColor: [color, '#e9ecef'],
                borderWidth: 0,
                cutout: '80%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            },
            rotation: -90,
            circumference: 180
        },
        plugins: [{
            beforeDraw: function(chart) {
                const ctx = chart.ctx;
                const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
                const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2;

                ctx.save();
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = color;
                ctx.fillText(value + '%', centerX, centerY - 10);
                
                ctx.font = '14px Arial';
                ctx.fillStyle = '#666';
                ctx.fillText(label, centerX, centerY + 15);
                ctx.restore();
            }
        }]
    });
}

// 创建柱状图
function createBarChart(canvasId, labels, data, label, color) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    return new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: label,
                data: data,
                backgroundColor: color,
                borderColor: color,
                borderWidth: 1,
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// 图表主题配置
const chartThemes = {
    light: {
        backgroundColor: '#ffffff',
        textColor: '#333333',
        gridColor: 'rgba(0, 0, 0, 0.1)'
    },
    dark: {
        backgroundColor: '#2d3748',
        textColor: '#ffffff',
        gridColor: 'rgba(255, 255, 255, 0.1)'
    }
};

// 应用图表主题
function applyChartTheme(chart, theme) {
    const themeConfig = chartThemes[theme];
    if (!themeConfig) return;

    chart.options.plugins.legend.labels.color = themeConfig.textColor;
    chart.options.scales.x.ticks.color = themeConfig.textColor;
    chart.options.scales.y.ticks.color = themeConfig.textColor;
    chart.options.scales.x.grid.color = themeConfig.gridColor;
    chart.options.scales.y.grid.color = themeConfig.gridColor;

    chart.update();
}
