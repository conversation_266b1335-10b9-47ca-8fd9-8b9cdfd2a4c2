// 能源管理页面图表配置

document.addEventListener('DOMContentLoaded', function() {
    initializeEnergyTrendChart();
    initializeEnergyStructureChart();
});

// 初始化能耗趋势图表
function initializeEnergyTrendChart() {
    const ctx = document.getElementById('energyTrendChart');
    if (!ctx) return;

    // 生成24小时数据
    const labels = [];
    const electricData = [];
    const waterData = [];
    const gasData = [];
    const steamData = [];
    
    for (let i = 0; i < 24; i++) {
        labels.push(i + ':00');
        electricData.push(Math.floor(Math.random() * 200) + 100);
        waterData.push(Math.floor(Math.random() * 80) + 40);
        gasData.push(Math.floor(Math.random() * 60) + 30);
        steamData.push(Math.floor(Math.random() * 40) + 20);
    }

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '电力 (kWh)',
                    data: electricData,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: '水资源 (m³)',
                    data: waterData,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: '天然气 (m³)',
                    data: gasData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: '蒸汽 (t)',
                    data: steamData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间',
                        color: '#666',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '消耗量',
                        color: '#666',
                        font: {
                            size: 12,
                            weight: 'bold'
                        }
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}

// 初始化能源结构饼图
function initializeEnergyStructureChart() {
    const ctx = document.getElementById('energyStructureChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['电力', '天然气', '水资源', '蒸汽', '其他'],
            datasets: [{
                data: [45, 25, 15, 10, 5],
                backgroundColor: [
                    '#ffc107',
                    '#007bff',
                    '#17a2b8',
                    '#28a745',
                    '#6c757d'
                ],
                borderColor: [
                    '#ffc107',
                    '#007bff',
                    '#17a2b8',
                    '#28a745',
                    '#6c757d'
                ],
                borderWidth: 2,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#007bff',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            return `${label}: ${value}%`;
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });
}
