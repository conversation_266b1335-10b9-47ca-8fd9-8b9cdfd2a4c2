/* 可视化大屏专用样式 */

.dashboard-fullscreen {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

/* 顶部状态栏 */
.dashboard-header {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.dashboard-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.current-time {
    font-size: 1.2rem;
    font-weight: 500;
    color: #ffd700;
}

.dashboard-controls .btn {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.dashboard-controls .btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 主要内容区域 */
.dashboard-content {
    padding-top: 80px;
    padding-bottom: 20px;
    min-height: 100vh;
}

/* 仪表板卡片 */
.dashboard-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 25px;
    height: 140px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00d4ff, #090979);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.energy-card::before {
    background: linear-gradient(90deg, #ffd700, #ff8c00);
}

.equipment-card::before {
    background: linear-gradient(90deg, #00ff88, #00b4db);
}

.alarm-card::before {
    background: linear-gradient(90deg, #ff6b6b, #ee5a24);
}

.environment-card::before {
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.card-icon {
    font-size: 3rem;
    margin-right: 20px;
    opacity: 0.8;
}

.energy-card .card-icon {
    color: #ffd700;
}

.equipment-card .card-icon {
    color: #00ff88;
}

.alarm-card .card-icon {
    color: #ff6b6b;
}

.environment-card .card-icon {
    color: #4ecdc4;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 8px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    line-height: 1;
}

.card-value .unit {
    font-size: 1rem;
    font-weight: 400;
    opacity: 0.7;
}

.card-trend {
    font-size: 0.85rem;
    font-weight: 500;
}

.trend-up {
    color: #00ff88;
}

.trend-down {
    color: #ff6b6b;
}

/* 图表卡片 */
.dashboard-chart-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    height: 400px;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.chart-controls .btn {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    margin-left: 5px;
}

.chart-controls .btn.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.chart-container {
    height: 320px;
    position: relative;
}

/* 列表卡片 */
.dashboard-list-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    height: 400px;
    display: flex;
    flex-direction: column;
}

.list-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.list-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

/* 设备列表 */
.equipment-list {
    flex: 1;
    overflow-y: auto;
}

.equipment-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.equipment-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.status-running {
    border-left-color: #00ff88;
}

.status-maintenance {
    border-left-color: #ffd700;
}

.status-fault {
    border-left-color: #ff6b6b;
}

.equipment-info {
    flex: 1;
}

.equipment-name {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 3px;
}

.equipment-location {
    font-size: 0.85rem;
    opacity: 0.7;
}

.equipment-status {
    display: flex;
    align-items: center;
    margin: 0 20px;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-running .status-indicator {
    background-color: #00ff88;
}

.status-maintenance .status-indicator {
    background-color: #ffd700;
}

.status-fault .status-indicator {
    background-color: #ff6b6b;
}

.status-text {
    font-size: 0.9rem;
    font-weight: 500;
}

.equipment-metrics {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.metric {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    min-width: 80px;
}

.metric-label {
    opacity: 0.7;
}

.metric-value {
    font-weight: 600;
}

/* 报警列表 */
.alarm-list {
    flex: 1;
    overflow-y: auto;
}

.alarm-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.alarm-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.alarm-critical {
    border-left-color: #ff6b6b;
}

.alarm-warning {
    border-left-color: #ffd700;
}

.alarm-info {
    border-left-color: #00d4ff;
}

.alarm-icon {
    margin-right: 15px;
    font-size: 1.2rem;
    margin-top: 2px;
}

.alarm-critical .alarm-icon {
    color: #ff6b6b;
}

.alarm-warning .alarm-icon {
    color: #ffd700;
}

.alarm-info .alarm-icon {
    color: #00d4ff;
}

.alarm-content {
    flex: 1;
}

.alarm-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 5px;
}

.alarm-detail {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.alarm-time {
    font-size: 0.75rem;
    opacity: 0.6;
}

.alarm-status {
    margin-left: 10px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.critical {
    background-color: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 1px solid #ff6b6b;
}

.status-badge.warning {
    background-color: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    border: 1px solid #ffd700;
}

.status-badge.info {
    background-color: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    border: 1px solid #00d4ff;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

/* 滚动条样式 */
.equipment-list::-webkit-scrollbar,
.alarm-list::-webkit-scrollbar {
    width: 6px;
}

.equipment-list::-webkit-scrollbar-track,
.alarm-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.equipment-list::-webkit-scrollbar-thumb,
.alarm-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.equipment-list::-webkit-scrollbar-thumb:hover,
.alarm-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .dashboard-card {
        height: 120px;
        padding: 20px;
    }
    
    .card-value {
        font-size: 2rem;
    }
    
    .card-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 10px 0;
    }
    
    .dashboard-title {
        font-size: 1.2rem;
    }
    
    .current-time {
        font-size: 1rem;
    }
    
    .dashboard-content {
        padding-top: 70px;
    }
    
    .dashboard-card {
        height: 100px;
        padding: 15px;
    }
    
    .card-value {
        font-size: 1.5rem;
    }
    
    .card-icon {
        font-size: 2rem;
        margin-right: 15px;
    }
    
    .dashboard-chart-card,
    .dashboard-list-card {
        height: 300px;
    }
}
